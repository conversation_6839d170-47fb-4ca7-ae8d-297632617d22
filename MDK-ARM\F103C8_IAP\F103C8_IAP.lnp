--cpu Cortex-M3
"f103c8_iap\startup_stm32f103xb.o"
"f103c8_iap\main.o"
"f103c8_iap\gpio.o"
"f103c8_iap\usart.o"
"f103c8_iap\stm32f1xx_it.o"
"f103c8_iap\stm32f1xx_hal_msp.o"
"f103c8_iap\stm32f1xx_hal_gpio_ex.o"
"f103c8_iap\stm32f1xx_hal_tim.o"
"f103c8_iap\stm32f1xx_hal_tim_ex.o"
"f103c8_iap\stm32f1xx_hal_uart.o"
"f103c8_iap\stm32f1xx_hal.o"
"f103c8_iap\stm32f1xx_hal_rcc.o"
"f103c8_iap\stm32f1xx_hal_rcc_ex.o"
"f103c8_iap\stm32f1xx_hal_gpio.o"
"f103c8_iap\stm32f1xx_hal_dma.o"
"f103c8_iap\stm32f1xx_hal_cortex.o"
"f103c8_iap\stm32f1xx_hal_pwr.o"
"f103c8_iap\stm32f1xx_hal_flash.o"
"f103c8_iap\stm32f1xx_hal_flash_ex.o"
"f103c8_iap\stm32f1xx_hal_exti.o"
"f103c8_iap\system_stm32f1xx.o"
"f103c8_iap\common.o"
"f103c8_iap\flash_if.o"
"f103c8_iap\menu.o"
"f103c8_iap\ymodem.o"
--library_type=microlib --strict --scatter "F103C8_IAP\F103C8_IAP.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\F103C8_IAP\F103C8_IAP.map" -o F103C8_IAP\F103C8_IAP.axf