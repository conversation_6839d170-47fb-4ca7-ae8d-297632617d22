Dependencies for Project 'F103C8_IAP', Target 'F103C8_IAP': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARM_Compiler_5.06u7
F (startup_stm32f103xb.s)(0x68A01112)(--cpu Cortex-M3 -g --apcs=interwork --pd "__MICROLIB SETA 1"

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

--pd "__UVISION_VERSION SETA 539" --pd "_RTE_ SETA 1" --pd "STM32F10X_MD SETA 1" --pd "_RTE_ SETA 1"

--list .\f103c8_iap\startup_stm32f103xb.lst --xref -o f103c8_iap\startup_stm32f103xb.o --depend f103c8_iap\startup_stm32f103xb.d)
F (../Core/Src/main.c)(0x68A05F26)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\main.o --omf_browse f103c8_iap\main.crf --depend f103c8_iap\main.d)
I (../Core/Inc/main.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
I (../Core/Inc/usart.h)(0x68A01112)
I (../Core/Inc/gpio.h)(0x68A01112)
I (..\UserCode\menu.h)(0x68A01112)
I (..\UserCode\flash_if.h)(0x68A06077)
I (..\UserCode\ymodem.h)(0x68A01112)
F (../Core/Src/gpio.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\gpio.o --omf_browse f103c8_iap\gpio.crf --depend f103c8_iap\gpio.d)
I (../Core/Inc/gpio.h)(0x68A01112)
I (../Core/Inc/main.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Core/Src/usart.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\usart.o --omf_browse f103c8_iap\usart.crf --depend f103c8_iap\usart.d)
I (../Core/Inc/usart.h)(0x68A01112)
I (../Core/Inc/main.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Core/Src/stm32f1xx_it.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_it.o --omf_browse f103c8_iap\stm32f1xx_it.crf --depend f103c8_iap\stm32f1xx_it.d)
I (../Core/Inc/main.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_it.h)(0x68A01112)
F (../Core/Src/stm32f1xx_hal_msp.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_msp.o --omf_browse f103c8_iap\stm32f1xx_hal_msp.crf --depend f103c8_iap\stm32f1xx_hal_msp.d)
I (../Core/Inc/main.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_gpio_ex.o --omf_browse f103c8_iap\stm32f1xx_hal_gpio_ex.crf --depend f103c8_iap\stm32f1xx_hal_gpio_ex.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_tim.o --omf_browse f103c8_iap\stm32f1xx_hal_tim.crf --depend f103c8_iap\stm32f1xx_hal_tim.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_tim_ex.o --omf_browse f103c8_iap\stm32f1xx_hal_tim_ex.crf --depend f103c8_iap\stm32f1xx_hal_tim_ex.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_uart.o --omf_browse f103c8_iap\stm32f1xx_hal_uart.crf --depend f103c8_iap\stm32f1xx_hal_uart.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal.o --omf_browse f103c8_iap\stm32f1xx_hal.crf --depend f103c8_iap\stm32f1xx_hal.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_rcc.o --omf_browse f103c8_iap\stm32f1xx_hal_rcc.crf --depend f103c8_iap\stm32f1xx_hal_rcc.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_rcc_ex.o --omf_browse f103c8_iap\stm32f1xx_hal_rcc_ex.crf --depend f103c8_iap\stm32f1xx_hal_rcc_ex.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_gpio.o --omf_browse f103c8_iap\stm32f1xx_hal_gpio.crf --depend f103c8_iap\stm32f1xx_hal_gpio.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_dma.o --omf_browse f103c8_iap\stm32f1xx_hal_dma.crf --depend f103c8_iap\stm32f1xx_hal_dma.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_cortex.o --omf_browse f103c8_iap\stm32f1xx_hal_cortex.crf --depend f103c8_iap\stm32f1xx_hal_cortex.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_pwr.o --omf_browse f103c8_iap\stm32f1xx_hal_pwr.crf --depend f103c8_iap\stm32f1xx_hal_pwr.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_flash.o --omf_browse f103c8_iap\stm32f1xx_hal_flash.crf --depend f103c8_iap\stm32f1xx_hal_flash.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_flash_ex.o --omf_browse f103c8_iap\stm32f1xx_hal_flash_ex.crf --depend f103c8_iap\stm32f1xx_hal_flash_ex.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\stm32f1xx_hal_exti.o --omf_browse f103c8_iap\stm32f1xx_hal_exti.crf --depend f103c8_iap\stm32f1xx_hal_exti.d)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (../Core/Src/system_stm32f1xx.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\system_stm32f1xx.o --omf_browse f103c8_iap\system_stm32f1xx.crf --depend f103c8_iap\system_stm32f1xx.d)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (..\UserCode\common.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\common.o --omf_browse f103c8_iap\common.crf --depend f103c8_iap\common.d)
I (..\UserCode\common.h)(0x68A01112)
I (../Core/Inc/main.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
I (../Core/Inc/usart.h)(0x68A01112)
F (..\UserCode\flash_if.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\flash_if.o --omf_browse f103c8_iap\flash_if.crf --depend f103c8_iap\flash_if.d)
I (..\UserCode\flash_if.h)(0x68A06077)
I (../Core/Inc/main.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
F (..\UserCode\menu.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\menu.o --omf_browse f103c8_iap\menu.crf --depend f103c8_iap\menu.d)
I (../Core/Inc/main.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
I (..\UserCode\common.h)(0x68A01112)
I (..\UserCode\flash_if.h)(0x68A06077)
I (..\UserCode\menu.h)(0x68A01112)
I (..\UserCode\ymodem.h)(0x68A01112)
I (../Core/Inc/usart.h)(0x68A01112)
F (..\UserCode\ymodem.c)(0x68A01112)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc -I ../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I ../Drivers/CMSIS/Include -I ..\UserCode --no-multibyte-chars

-I.\RTE\_F103C8_IAP

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F1xx_DFP\2.4.1\Device\Include

-D__UVISION_VERSION="539" -D_RTE_ -DSTM32F10X_MD -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F103xB

-o f103c8_iap\ymodem.o --omf_browse f103c8_iap\ymodem.crf --depend f103c8_iap\ymodem.d)
I (..\UserCode\flash_if.h)(0x68A06077)
I (../Core/Inc/main.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal.h)(0x68A01112)
I (../Core/Inc/stm32f1xx_hal_conf.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_def.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f1xx.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/stm32f103xb.h)(0x68A01112)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stdint.h)(0x5E8E3CC2)
I (../Drivers/CMSIS/Include/cmsis_version.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_compiler.h)(0x68A01112)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x68A01112)
I (../Drivers/CMSIS/Device/ST/STM32F1xx/Include/system_stm32f1xx.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\stddef.h)(0x5E8E3CC2)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_rcc_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_gpio_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_exti.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_dma_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_cortex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_flash_ex.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_pwr.h)(0x68A01112)
I (../Drivers/STM32F1xx_HAL_Driver/Inc/stm32f1xx_hal_uart.h)(0x68A01112)
I (..\UserCode\common.h)(0x68A01112)
I (..\UserCode\ymodem.h)(0x68A01112)
I (C:\Keil_v5\ARM\ARM_Compiler_5.06u7\include\string.h)(0x5E8E3CC2)
I (..\UserCode\menu.h)(0x68A01112)
I (../Core/Inc/usart.h)(0x68A01112)
